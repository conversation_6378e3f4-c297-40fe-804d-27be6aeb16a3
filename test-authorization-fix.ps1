# Test authorization fix and new partner users endpoint
$accessToken = "VTgxeHgyUFhuUWtuOW56YVA3elVUTFV2Qkk3cDRxeXNxSXM4MWluMHVjZVROaWhHcXJCNVBGQzEvR1hIRzcyWDJpWFlYeEl1QkRjUUp2b3JsTWJvRE03NmEyRlllcDdETDhlVUVKUzZZMzMzVlM1VUlRR3NWSlQ1WnVZdDZRdzZUbWxiRlNXaitncHhoaXpmenV2Q1pURmR6VmMrZnRySDBBR0ZjcHdwWHFKNFIyT0tFUzM2czFDQ1htZlhBU3BYOVpvd2pkZW52bk4vTjhNczVQWi9CMExCQVRoZUNMVUlWWktKcHBiZVFTWm16WWRORHR5dmJsNGJ3WDFxdlV6QlJ4Mk9lQzcrYTV5Sk9LbGhsOUM2YWpaWFFid0EyOWxrdjBFMDlOQmt5M2dBYzVEa2hKSXNPMU11dEVvRysydG1DRnNKcGh1cnkwcndlZ054cmtxVTdyb1lpVmpZa3ZrdHFVYnZnMFA2RThvMDV2cTBqT3BOekNFRXJRMlZITFJWNjJ1Uk8vNjhCcW00ckZKdDdEa1B6dExCNERWVzlYS0JEQjJibXJZWnZxRHJiR0xRSjVqK2RMNVNPenVURFgvK2hhKytpNEtOSU4ybW02ZG1yTmdSR2VYOTU5ZW1JZy9qTDlHRGY0dkJ4YTQ1c1FERmdCMmYvb2sxTWEyS3R5WVFYVTJZSXJYWDlBQW1vbE5JNzhBb2pWY2JmMzU4a1BVVFJoZEFXTnhWU0h5a1YwTmM0ZmpSaVVSTnFDNWZsYnBCWkxieXdXTVp3MjR5b3JMb3MyNElKRzlpVXdzcFdrdXJ5eVN0L3Rwa0JGTUhpS2hpN0xjZXZEdVI5MGhyTE5XL3ROMS9rNXROSFBmdDNRMTk3ajkwOTBOQTdrMnBSYU1GZ0psbUp0OW9tR3hEV3RmektJYWNDVEcxMHkvSitiTVNER2VxNjl5cndIQURManUwQ3c5T294OHc2eC9WdkJ2T0YvUndpNnQrcDQ4aTlpay9JTFdLbTgrY3JTUGVEOXpIanVUU2tob0RtRW1JR3dWaXhwckFZM0tFaTJxMGw1U1doZm40STJNKzY5STZKV0R1OGEyeFY5WmdNUUhVSlQ5N0srSjg4VDR3Y3VxaDVFTFpFVHFwYk9ZZXRzNjBmc21KcnZaZ2NBd0hlRDJXTU1yc04zZnF6QVJ6aXRGT04xeTVHY015VXRBNnNCM1ZrWjE2NUlUeHRjWVBrOWIvdUhNdVJmcUd2Ui9tREMzZUtnTDFQY3VGWWxteDdDY2Rlbng0dnRiMFlLWHBpRUV0WFYxTlF2VjRHSEVVOGlYOG1lN3NnaUdtVG9EZkIyaFBjVVJ6d1dKNUxhUXR6N2plNHpNWitPcnlsZU1EYm81cXVCWEJtWU9WcXJaMUQrc1o2bk5nbUtpNUM2WVh1NjhiVDkvbGE3V2ZSTEtwOXBpMXhkcEhzZGN5bDJYY25kUm9DbEpqNmdyVlhVczd1Qk9yNDV5V0FGN0tmQT09LS1UU2FuMDlpd3Fwdld5OURaLS04R2M5MERFK2Z2RnZnODJUSEFzYWNRPT0%3D"

# Create a web session to handle cookies properly
$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession
$cookie = New-Object System.Net.Cookie("access_token", $accessToken, "/", "ng-support-local.dev.dev1.ngnair.com")
$session.Cookies.Add($cookie)

$headers = @{
    "Content-Type" = "application/json"
}

$baseUrl = "http://ng-support-local.dev.dev1.ngnair.com:3040"

Write-Host "Testing Authorization Fix and New Partner Users Endpoint..." -ForegroundColor Green
Write-Host "Domain: $baseUrl" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Green

# Test 1: GraphQL createComment (should work now with authorization fix)
Write-Host "`n1. Testing createComment GraphQL mutation (authorization fix)..." -ForegroundColor Yellow

$createCommentQuery = @{
    query = "mutation CreateComment(`$data: CreateCommentDto!) { createComment(data: `$data) { id message authorId ticketId createdAt } }"
    variables = @{
        data = @{
            message = "Test comment from GraphQL with authorization fix"
            ticketId = "test-ticket-id"
        }
    }
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/graphql" -Method Post -Headers $headers -Body $createCommentQuery -WebSession $session
    
    if ($response.errors) {
        Write-Host "❌ Error:" -ForegroundColor Red
        $response.errors | ForEach-Object { Write-Host "  - $($_.message)" -ForegroundColor Red }
    } else {
        Write-Host "✅ Success! Comment creation working with authorization fix" -ForegroundColor Green
        $response.data | ConvertTo-Json -Depth 2 | Write-Host -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: REST API partner users endpoint
Write-Host "`n2. Testing REST API /auth/partner-users endpoint..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/auth/partner-users" -Method Get -Headers $headers -WebSession $session
    
    Write-Host "✅ Success! Partner users REST endpoint working" -ForegroundColor Green
    Write-Host "Found $($response.Count) partner users:" -ForegroundColor Cyan
    $response | ForEach-Object {
        Write-Host "  - $($_.firstName) $($_.lastName) ($($_.email)) - Partner: $($_.partnerOrgId)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: GraphQL partner users query
Write-Host "`n3. Testing GraphQL partnerUsers query..." -ForegroundColor Yellow

$partnerUsersQuery = @{
    query = "query GetPartnerUsers { partnerUsers { userId firstName lastName email partnerOrgId } }"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/graphql" -Method Post -Headers $headers -Body $partnerUsersQuery -WebSession $session
    
    if ($response.errors) {
        Write-Host "❌ Error:" -ForegroundColor Red
        $response.errors | ForEach-Object { Write-Host "  - $($_.message)" -ForegroundColor Red }
    } else {
        Write-Host "✅ Success! Partner users GraphQL query working" -ForegroundColor Green
        Write-Host "Found $($response.data.partnerUsers.Count) partner users:" -ForegroundColor Cyan
        $response.data.partnerUsers | ForEach-Object {
            Write-Host "  - $($_.firstName) $($_.lastName) ($($_.email)) - Partner: $($_.partnerOrgId)" -ForegroundColor Cyan
        }
    }
} catch {
    Write-Host "❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test other protected endpoints to ensure authorization still works
Write-Host "`n4. Testing other protected endpoints..." -ForegroundColor Yellow

$protectedQueries = @(
    @{
        name = "categories"
        query = "query GetCategories { categories { id name description } }"
    },
    @{
        name = "tickets"
        query = "query GetTickets { tickets { id subject description status } }"
    },
    @{
        name = "customValues"
        query = "query GetCustomValues { customValues { key name value } }"
    }
)

foreach ($test in $protectedQueries) {
    Write-Host "`n  Testing $($test.name)..." -ForegroundColor Gray
    
    $body = @{
        query = $test.query
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/graphql" -Method Post -Headers $headers -Body $body -WebSession $session
        
        if ($response.errors) {
            Write-Host "    ❌ Error: $($response.errors[0].message)" -ForegroundColor Red
        } else {
            Write-Host "    ✅ Success!" -ForegroundColor Green
        }
    } catch {
        Write-Host "    ❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n================================" -ForegroundColor Green
Write-Host "Authorization fix and partner users testing completed!" -ForegroundColor Green
