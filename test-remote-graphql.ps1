# Test GraphQL endpoints on remote domain
$accessToken = "VTgxeHgyUFhuUWtuOW56YVA3elVUTFV2Qkk3cDRxeXNxSXM4MWluMHVjZVROaWhHcXJCNVBGQzEvR1hIRzcyWDJpWFlYeEl1QkRjUUp2b3JsTWJvRE03NmEyRlllcDdETDhlVUVKUzZZMzMzVlM1VUlRR3NWSlQ1WnVZdDZRdzZUbWxiRlNXaitncHhoaXpmenV2Q1pURmR6VmMrZnRySDBBR0ZjcHdwWHFKNFIyT0tFUzM2czFDQ1htZlhBU3BYOVpvd2pkZW52bk4vTjhNczVQWi9CMExCQVRoZUNMVUlWWktKcHBiZVFTWm16WWRORHR5dmJsNGJ3WDFxdlV6QlJ4Mk9lQzcrYTV5Sk9LbGhsOUM2YWpaWFFid0EyOWxrdjBFMDlOQmt5M2dBYzVEa2hKSXNPMU11dEVvRysydG1DRnNKcGh1cnkwcndlZ054cmtxVTdyb1lpVmpZa3ZrdHFVYnZnMFA2RThvMDV2cTBqT3BOekNFRXJRMlZITFJWNjJ1Uk8vNjhCcW00ckZKdDdEa1B6dExCNERWVzlYS0JEQjJibXJZWnZxRHJiR0xRSjVqK2RMNVNPenVURFgvK2hhKytpNEtOSU4ybW02ZG1yTmdSR2VYOTU5ZW1JZy9qTDlHRGY0dkJ4YTQ1c1FERmdCMmYvb2sxTWEyS3R5WVFYVTJZSXJYWDlBQW1vbE5JNzhBb2pWY2JmMzU4a1BVVFJoZEFXTnhWU0h5a1YwTmM0ZmpSaVVSTnFDNWZsYnBCWkxieXdXTVp3MjR5b3JMb3MyNElKRzlpVXdzcFdrdXJ5eVN0L3Rwa0JGTUhpS2hpN0xjZXZEdVI5MGhyTE5XL3ROMS9rNXROSFBmdDNRMTk3ajkwOTBOQTdrMnBSYU1GZ0psbUp0OW9tR3hEV3RmektJYWNDVEcxMHkvSitiTVNER2VxNjl5cndIQURManUwQ3c5T294OHc2eC9WdkJ2T0YvUndpNnQrcDQ4aTlpay9JTFdLbTgrY3JTUGVEOXpIanVUU2tob0RtRW1JR3dWaXhwckFZM0tFaTJxMGw1U1doZm40STJNKzY5STZKV0R1OGEyeFY5WmdNUUhVSlQ5N0srSjg4VDR3Y3VxaDVFTFpFVHFwYk9ZZXRzNjBmc21KcnZaZ2NBd0hlRDJXTU1yc04zZnF6QVJ6aXRGT04xeTVHY015VXRBNnNCM1ZrWjE2NUlUeHRjWVBrOWIvdUhNdVJmcUd2Ui9tREMzZUtnTDFQY3VGWWxteDdDY2Rlbng0dnRiMFlLWHBpRUV0WFYxTlF2VjRHSEVVOGlYOG1lN3NnaUdtVG9EZkIyaFBjVVJ6d1dKNUxhUXR6N2plNHpNWitPcnlsZU1EYm81cXVCWEJtWU9WcXJaMUQrc1o2bk5nbUtpNUM2WVh1NjhiVDkvbGE3V2ZSTEtwOXBpMXhkcEhzZGN5bDJYY25kUm9DbEpqNmdyVlhVczd1Qk9yNDV5V0FGN0tmQT09LS1UU2FuMDlpd3Fwdld5OURaLS04R2M5MERFK2Z2RnZnODJUSEFzYWNRPT0%3D"

# Create a web session to handle cookies properly
$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession
$cookie = New-Object System.Net.Cookie("access_token", $accessToken, "/", "ng-support-local.dev.dev1.ngnair.com")
$session.Cookies.Add($cookie)

$headers = @{
    "Content-Type" = "application/json"
}

$baseUrl = "http://ng-support-local.dev.dev1.ngnair.com:3040/graphql"

# Test basic queries first
$queries = @(
    @{
        name = "authPing - Test auth"
        query = "query AuthPing { authPing }"
    },
    @{
        name = "health - Health check"
        query = "query Health { health }"
    },
    @{
        name = "authMe - Get current user JWT"
        query = "query GetCurrentUserJWT { authMe { iss sub aud exp iat jti sid azp ent_set perm_v amr auth_time email } }"
    },
    @{
        name = "me - Get current user (alias)"
        query = "query GetMe { me { iss sub aud exp iat jti sid azp ent_set perm_v amr auth_time email } }"
    },
    @{
        name = "authUsers - Get all users from auth service"
        query = "query GetAuthUsers { authUsers { id email firstName lastName role active phone country verifiedEmail verifiedPhone mfaEnabled partnerId permissions accountId createdAt } }"
    },
    @{
        name = "users - Get users with pagination"
        query = "query GetUsers { users { items { id email firstName lastName role active } total pages page limit } }"
    },
    @{
        name = "activeUsers - Get active users"
        query = "query GetActiveUsers { activeUsers { id email firstName lastName role active } }"
    }
)

Write-Host "Testing GraphQL endpoints on remote domain..." -ForegroundColor Green
Write-Host "Domain: $baseUrl" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Green

foreach ($test in $queries) {
    Write-Host "`nTesting: $($test.name)" -ForegroundColor Yellow
    Write-Host "Query: $($test.query)" -ForegroundColor Gray
    
    $body = @{
        query = $test.query
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri $baseUrl -Method Post -Headers $headers -Body $body -WebSession $session
        
        if ($response.errors) {
            Write-Host "❌ Error:" -ForegroundColor Red
            $response.errors | ForEach-Object { Write-Host "  - $($_.message)" -ForegroundColor Red }
        } else {
            Write-Host "✅ Success!" -ForegroundColor Green
            if ($response.data) {
                $response.data | ConvertTo-Json -Depth 3 | Write-Host -ForegroundColor Cyan
            }
        }
    } catch {
        Write-Host "❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Milliseconds 500
}

Write-Host "`n================================" -ForegroundColor Green
Write-Host "Remote GraphQL testing completed!" -ForegroundColor Green
