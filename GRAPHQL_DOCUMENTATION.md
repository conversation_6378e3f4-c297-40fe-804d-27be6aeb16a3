# GraphQL API Documentation

## Overview

This document provides comprehensive documentation for the GraphQL API implementation in the support-backend application. The GraphQL API provides a unified interface for all backend operations with proper authentication and authorization.

## Authentication

The GraphQL API uses cookie-based authentication with encrypted access tokens, identical to the REST API implementation.

### Authentication Method
- **Type**: Cookie-based authentication
- **Cookie Name**: `access_token`
- **Format**: Encrypted JWT token (Rails-compatible AES-256-GCM encryption)
- **Domain**: Must match the server domain for proper cookie handling

### Example Authentication Header
```bash
curl -X POST "http://localhost:3040/graphql" \
  -H "Content-Type: application/json" \
  -H "Cookie: access_token=YOUR_ENCRYPTED_TOKEN_HERE" \
  -d '{"query": "query { authMe { sub email } }"}'
```

## Available Endpoints

### Base URL
- **Local Development**: `http://localhost:3040/graphql`
- **Remote Development**: `http://ng-support-local.dev.dev1.ngnair.com/graphql`

### GraphQL Playground
- **Local**: `http://localhost:3040/graphql` (with introspection enabled)
- **Settings**: `'request.credentials': 'include'` for cookie handling

## Query Reference

### Authentication Queries

#### 1. authMe
Get current user's JWT payload from authentication service.

```graphql
query GetCurrentUserJWT {
  authMe {
    iss
    sub
    aud
    exp
    iat
    jti
    sid
    azp
    ent_set
    perm_v
    amr
    auth_time
    email
  }
}
```

#### 2. me
Alias for `authMe` - returns the same JWT payload.

```graphql
query GetMe {
  me {
    iss
    sub
    aud
    exp
    iat
    jti
    sid
    azp
    ent_set
    perm_v
    amr
    auth_time
    email
  }
}
```

#### 3. authUsers
Get all users from external authentication service.

```graphql
query GetAuthUsers {
  authUsers {
    id
    email
    firstName
    lastName
    role
    active
    phone
    country
    verifiedEmail
    verifiedPhone
    mfaEnabled
    partnerId
    permissions
    accountId
    createdAt
  }
}
```

#### 4. authUser
Get specific user by ID from external authentication service.

```graphql
query GetAuthUser($id: String!) {
  authUser(id: $id) {
    id
    email
    firstName
    lastName
    role
    active
    phone
    country
    verifiedEmail
    verifiedPhone
    mfaEnabled
    partnerId
    permissions
    accountId
    createdAt
  }
}
```

#### 5. user
Alias for `authUser` - get user by ID.

```graphql
query GetUser($id: String!) {
  user(id: $id) {
    id
    email
    firstName
    lastName
    role
    active
    phone
    country
    verifiedEmail
    verifiedPhone
    mfaEnabled
    partnerId
    permissions
    accountId
    createdAt
  }
}
```

#### 6. users
Get paginated list of users with optional filtering.

```graphql
query GetUsers($params: GetUsersDto) {
  users(params: $params) {
    items {
      id
      email
      firstName
      lastName
      role
      active
      phone
      country
      verifiedEmail
      verifiedPhone
      mfaEnabled
      partnerId
      permissions
      accountId
      createdAt
    }
    total
    pages
    page
    limit
  }
}
```

**Variables Example:**
```json
{
  "params": {
    "active": true,
    "role": "admin",
    "search": "john",
    "page": 1,
    "limit": 10
  }
}
```

#### 7. activeUsers
Get all active users.

```graphql
query GetActiveUsers {
  activeUsers {
    id
    email
    firstName
    lastName
    role
    active
    phone
    country
    verifiedEmail
    verifiedPhone
    mfaEnabled
    partnerId
    permissions
    accountId
    createdAt
  }
}
```

#### 8. usersByRole
Get users filtered by role.

```graphql
query GetUsersByRole($role: String!) {
  usersByRole(role: $role) {
    id
    email
    firstName
    lastName
    role
    active
    phone
    country
    verifiedEmail
    verifiedPhone
    mfaEnabled
    partnerId
    permissions
    accountId
    createdAt
  }
}
```

#### 9. validateToken
Validate an access token.

```graphql
query ValidateToken($token: String!) {
  validateToken(token: $token)
}
```

#### 10. authPing
Simple ping endpoint to test authentication setup.

```graphql
query AuthPing {
  authPing
}
```

### Health Check Queries

#### 1. health
Get service health status.

```graphql
query Health {
  health
}
```

#### 2. ping
Simple ping endpoint.

```graphql
query Ping {
  ping
}
```

### Category Queries

#### 1. categories
Get all categories.

```graphql
query GetCategories {
  categories {
    id
    name
    description
    type
    autoAssignTo
    timeoutMinutes
    escalateTo
    createdAt
    updatedAt
  }
}
```

#### 2. category
Get specific category by ID.

```graphql
query GetCategory($id: ID!) {
  category(id: $id) {
    id
    name
    description
    type
    autoAssignTo
    timeoutMinutes
    escalateTo
    createdAt
    updatedAt
  }
}
```

### Ticket Queries

#### 1. tickets
Get all tickets (scope-aware).

```graphql
query GetTickets {
  tickets {
    id
    subject
    description
    status
    priority
    accountId
    partnerId
    assignedTo
    createdBy
    lastUpdatedBy
    createdAt
    updatedAt
    categoryId
    firstName
    lastName
    email
    partnerUserId
    partnerRole
    partnerOrgId
    entSet
  }
}
```

#### 2. ticket
Get specific ticket by ID.

```graphql
query GetTicket($id: String!) {
  ticket(id: $id) {
    id
    subject
    description
    status
    priority
    accountId
    partnerId
    assignedTo
    createdBy
    lastUpdatedBy
    createdAt
    updatedAt
    categoryId
    firstName
    lastName
    email
    partnerUserId
    partnerRole
    partnerOrgId
    entSet
  }
}
```

### Comment Queries

#### 1. commentsByTicket
Get comments for a specific ticket.

```graphql
query GetCommentsByTicket($ticketId: String!) {
  commentsByTicket(ticketId: $ticketId) {
    id
    message
    authorId
    ticketId
    createdAt
    updatedAt
  }
}
```

### Custom Value Queries

#### 1. customValues
Get custom values with optional category filter.

```graphql
query GetCustomValues($category: String) {
  customValues(category: $category) {
    key
    name
    description
    value
    type
    category
    isSystem
    createdBy
    lastUpdatedBy
    createdAt
    updatedAt
  }
}
```

#### 2. customValue
Get specific custom value by key.

```graphql
query GetCustomValue($key: String!) {
  customValue(key: $key) {
    key
    name
    description
    value
    type
    category
    isSystem
    createdBy
    lastUpdatedBy
    createdAt
    updatedAt
  }
}
```

### File Queries

#### 1. supportFiles
Get support files with optional filters.

```graphql
query GetSupportFiles($ticketId: String, $commentId: String) {
  supportFiles(ticketId: $ticketId, commentId: $commentId) {
    id
    filename
    fileSize
    fileUrl
    mimeType
    uploadedBy
    ticketId
    commentId
    uploadedAt
  }
}
```

#### 2. supportFile
Get specific support file by ID.

```graphql
query GetSupportFile($id: String!) {
  supportFile(id: $id) {
    id
    filename
    fileSize
    fileUrl
    mimeType
    uploadedBy
    ticketId
    commentId
    uploadedAt
  }
}
```

## Mutation Reference

### Ticket Mutations

#### 1. createTicket
Create a new support ticket.

```graphql
mutation CreateTicket($data: CreateTicketDto!) {
  createTicket(data: $data) {
    id
    subject
    description
    status
    priority
    categoryId
    createdBy
    lastUpdatedBy
    createdAt
    updatedAt
  }
}
```

**Variables Example:**
```json
{
  "data": {
    "subject": "Test Ticket",
    "description": "This is a test ticket",
    "priority": "MEDIUM",
    "categoryId": "category-id-here"
  }
}
```

#### 2. updateTicket
Update an existing ticket.

```graphql
mutation UpdateTicket($id: String!, $data: UpdateTicketDto!) {
  updateTicket(id: $id, data: $data) {
    id
    subject
    description
    status
    priority
    categoryId
    lastUpdatedBy
    updatedAt
  }
}
```

### Comment Mutations

#### 1. createComment
Add a comment to a ticket.

```graphql
mutation CreateComment($data: CreateCommentDto!) {
  createComment(data: $data) {
    id
    message
    authorId
    ticketId
    createdAt
    updatedAt
  }
}
```

**Variables Example:**
```json
{
  "data": {
    "message": "This is a comment",
    "ticketId": "ticket-id-here"
  }
}
```

### Category Mutations

#### 1. createCategory
Create a new category (Global Admin only).

```graphql
mutation CreateCategory($data: CreateCategoryDto!) {
  createCategory(data: $data) {
    id
    name
    description
    type
    autoAssignTo
    timeoutMinutes
    escalateTo
    createdAt
    updatedAt
  }
}
```

#### 2. updateCategory
Update an existing category (Global Admin only).

```graphql
mutation UpdateCategory($id: ID!, $data: UpdateCategoryDto!) {
  updateCategory(id: $id, data: $data) {
    id
    name
    description
    type
    autoAssignTo
    timeoutMinutes
    escalateTo
    updatedAt
  }
}
```

#### 3. deleteCategory
Delete a category (Global Admin only).

```graphql
mutation DeleteCategory($id: ID!) {
  deleteCategory(id: $id) {
    id
    name
  }
}
```

### Custom Value Mutations

#### 1. createCustomValue
Create a new custom value (Global Admin only).

```graphql
mutation CreateCustomValue($data: CreateCustomValueDto!) {
  createCustomValue(data: $data) {
    key
    name
    description
    value
    type
    category
    isSystem
    createdBy
    lastUpdatedBy
    createdAt
    updatedAt
  }
}
```

#### 2. updateCustomValue
Update an existing custom value (Global Admin only).

```graphql
mutation UpdateCustomValue($key: String!, $data: UpdateCustomValueDto!) {
  updateCustomValue(key: $key, data: $data) {
    key
    name
    description
    value
    type
    category
    isSystem
    lastUpdatedBy
    updatedAt
  }
}
```

#### 3. deleteCustomValue
Delete a custom value (Global Admin only).

```graphql
mutation DeleteCustomValue($key: String!) {
  deleteCustomValue(key: $key) {
    key
    name
  }
}
```

## Authorization

The GraphQL API implements the same scope-based authorization as the REST API:

### Scope Levels
- **GLOBAL_ADMIN**: Full access to all resources and admin operations
- **PARTNER_SUPPORT_ADMIN**: Admin access within partner scope
- **PARTNER_SUPPORT_USER**: User access within partner scope
- **ACCOUNT_SUPPORT_ADMIN**: Admin access within account scope
- **ACCOUNT_SUPPORT_USER**: User access within account scope

### Guards
- **GraphQLAuthGuard**: Validates authentication and extracts user context
- **ScopeGuard**: Resolves user scopes and applies authorization rules
- **@SupportAccess()**: Requires any support scope
- **@GlobalAdminOnly()**: Requires global admin scope
- **@PartnerSupportAccess()**: Requires partner-level support access

## Error Handling

GraphQL errors are returned in the standard GraphQL error format:

```json
{
  "errors": [
    {
      "message": "Authentication required - no access token",
      "locations": [{"line": 2, "column": 3}],
      "path": ["authMe"]
    }
  ],
  "data": null
}
```

### Common Error Messages
- `"Authentication required - no access token"`: No access token provided
- `"Authentication failed"`: Invalid or expired token
- `"Forbidden resource"`: Insufficient permissions
- `"Invalid token"`: Token validation failed

## Testing

### PowerShell Test Script
Use the provided `test-graphql-complete.ps1` script to test all endpoints:

```powershell
powershell -ExecutionPolicy Bypass -File test-graphql-complete.ps1
```

### Manual Testing with curl
```bash
# Test authentication
curl -X POST "http://localhost:3040/graphql" \
  -H "Content-Type: application/json" \
  -H "Cookie: access_token=YOUR_TOKEN_HERE" \
  -d '{"query": "query { authMe { sub email } }"}'

# Test health check
curl -X POST "http://localhost:3040/graphql" \
  -H "Content-Type: application/json" \
  -d '{"query": "query { health }"}'
```

### GraphQL Playground
Access the GraphQL Playground at `http://localhost:3040/graphql` for interactive testing. Make sure to set the request credentials to include cookies:

```javascript
{
  "request.credentials": "include"
}
```

## Implementation Status

✅ **Completed Features:**
- Authentication queries (authMe, me, authUsers, authUser, user, users, activeUsers, usersByRole, validateToken, authPing)
- Health check queries (health, ping)
- Category queries and mutations
- Ticket queries and mutations
- Comment queries and mutations
- Custom value queries and mutations
- File queries
- Proper authentication and authorization
- Scope-based access control
- GraphQL types and input DTOs
- Error handling

🔄 **Current Status:**
- All GraphQL resolvers implemented
- Authentication system working
- Authorization guards in place
- Comprehensive documentation provided

## Next Steps

1. **Test with Valid Token**: Ensure you have a valid access token for testing
2. **Verify Domain Configuration**: Check that the domain configuration matches your environment
3. **Monitor Logs**: Check application logs for authentication and authorization issues
4. **Performance Testing**: Test with larger datasets and concurrent requests
5. **Security Review**: Validate that all security measures are properly implemented

## Support

For issues or questions regarding the GraphQL API implementation, please refer to:
- Application logs in Docker containers
- GraphQL Playground for interactive testing
- This documentation for query examples and authentication details
