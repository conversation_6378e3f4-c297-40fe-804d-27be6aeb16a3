import { Resolver, Query } from '@nestjs/graphql';
import { Logger } from '@nestjs/common';

@Resolver()
export class HealthResolver {
  private readonly logger = new Logger(HealthResolver.name);

  @Query(() => String, { 
    name: 'health', 
    description: 'Health check endpoint for GraphQL' 
  })
  async health(): Promise<string> {
    this.logger.log('🔍 [HEALTH RESOLVER] health query called');
    return JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'support-backend',
      graphql: true
    });
  }

  @Query(() => String, { 
    name: 'ping', 
    description: 'Simple ping endpoint' 
  })
  async ping(): Promise<string> {
    this.logger.log('🔍 [HEALTH RESOLVER] ping query called');
    return 'pong';
  }
}
