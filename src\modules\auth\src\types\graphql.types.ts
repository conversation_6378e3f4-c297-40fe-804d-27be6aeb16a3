import { ObjectType, Field, Int } from '@nestjs/graphql';

/**
 * GraphQL type for JWT payload - matches the actual Ruby JWT structure exactly
 * Returns ONLY actual JWT fields (no computed or hardcoded fields)
 */
@ObjectType('JWTPayload')
export class JWTPayloadGraphQL {
  // Standard JWT fields
  @Field(() => String, { description: 'JWT issuer' })
  iss: string;

  @Field(() => String, { description: 'User ID (subject)' })
  sub: string;

  @Field(() => String, { description: 'JWT audience' })
  aud: string;

  @Field(() => Int, { description: 'Expiration time (Unix timestamp)' })
  exp: number;

  @Field(() => Int, { description: 'Issued at time (Unix timestamp)' })
  iat: number;

  @Field(() => String, { description: 'JWT ID (UUID)' })
  jti: string;

  // NGNair-specific fields
  @Field(() => String, { description: 'Session ID (e.g., "sess_35ee063223007077")' })
  sid: string;

  @Field(() => String, { description: 'Authorized party (e.g., "webapp")' })
  azp: string;

  @Field(() => String, { description: 'Entity set (hex string, e.g., "de2e18a49c8b")' })
  ent_set: string;

  @Field(() => Int, { description: 'Permission version (e.g., 0)' })
  perm_v: number;

  @Field(() => [String], { description: 'Authentication methods (e.g., ["pwd"])' })
  amr: string[];

  @Field(() => Int, { description: 'Authentication time (Unix timestamp)' })
  auth_time: number;

  // Optional fields (may not be present in all JWTs)
  @Field(() => String, { nullable: true, description: 'User email (optional - may not be present in all JWTs)' })
  email?: string;
}

/**
 * GraphQL type for User data from external auth service
 * This represents the user data structure returned by the external authentication service
 */
@ObjectType('AuthUser')
export class UserGraphQL {
  @Field(() => String, { description: 'User unique identifier' })
  id: string;

  @Field(() => String, { description: 'User email address' })
  email: string;

  @Field(() => String, { nullable: true, description: 'User first name' })
  firstName?: string;

  @Field(() => String, { nullable: true, description: 'User last name' })
  lastName?: string;

  @Field(() => String, { nullable: true, description: 'User username' })
  username?: string;

  @Field(() => String, { nullable: true, description: 'User role' })
  role?: string;

  @Field(() => [String], { nullable: true, description: 'User permissions' })
  permissions?: string[];

  @Field(() => String, { nullable: true, description: 'User phone number' })
  phone?: string;

  @Field(() => String, { nullable: true, description: 'User country' })
  country?: string;

  @Field(() => Boolean, { nullable: true, description: 'Email verification status' })
  verifiedEmail?: boolean;

  @Field(() => Boolean, { nullable: true, description: 'Phone verification status' })
  verifiedPhone?: boolean;

  @Field(() => String, { nullable: true, description: 'Partner ID' })
  partnerId?: string;

  @Field(() => Boolean, { nullable: true, description: 'MFA enabled status' })
  mfaEnabled?: boolean;

  @Field(() => Boolean, { nullable: true, description: 'User active status' })
  active?: boolean;

  @Field(() => String, { nullable: true, description: 'Account ID' })
  accountId?: string;

  @Field(() => String, { nullable: true, description: 'User creation timestamp' })
  createdAt?: string;

  @Field(() => String, { nullable: true, description: 'User last update timestamp' })
  updatedAt?: string;

  // External auth service specific fields
  @Field(() => [String], { nullable: true, description: 'User accounts' })
  accounts?: any[];

  @Field(() => [String], { nullable: true, description: 'User partners' })
  partners?: any[];

  @Field(() => [String], { nullable: true, description: 'Active accounts' })
  active_accounts?: any[];

  @Field(() => [String], { nullable: true, description: 'Active partners' })
  active_partners?: any[];

  @Field(() => String, { nullable: true, description: 'First name (alternative field)' })
  first_name?: string;

  @Field(() => String, { nullable: true, description: 'Last name (alternative field)' })
  last_name?: string;
}

/**
 * GraphQL type for CurrentUser - represents the current authenticated user
 * This matches the User type from the schema but is specifically for the 'me' query
 */
@ObjectType('CurrentUser')
export class CurrentUserGraphQL {
  @Field(() => String, { description: 'User unique identifier' })
  id: string;

  @Field(() => String, { description: 'User email address' })
  email: string;

  @Field(() => String, { description: 'User first name' })
  firstName: string;

  @Field(() => String, { description: 'User last name' })
  lastName: string;

  @Field(() => String, { description: 'User role' })
  role: string;

  @Field(() => Boolean, { description: 'User active status' })
  active: boolean;

  @Field(() => String, { nullable: true, description: 'User phone number' })
  phone?: string;

  @Field(() => String, { nullable: true, description: 'User country' })
  country?: string;

  @Field(() => Boolean, { description: 'Email verification status' })
  verifiedEmail: boolean;

  @Field(() => Boolean, { description: 'Phone verification status' })
  verifiedPhone: boolean;

  @Field(() => Boolean, { description: 'MFA enabled status' })
  mfaEnabled: boolean;

  @Field(() => [String], { description: 'Partner IDs' })
  partnerId: string[];

  @Field(() => [String], { nullable: true, description: 'User permissions' })
  permissions?: string[];

  @Field(() => String, { nullable: true, description: 'Account ID' })
  accountId?: string;

  @Field(() => String, { description: 'User creation timestamp' })
  createdAt: string;
}

/**
 * GraphQL type for paginated user list response
 */
@ObjectType('UserListResponse')
export class UserListResponseGraphQL {
  @Field(() => [UserGraphQL], { description: 'List of users' })
  items: UserGraphQL[];

  @Field(() => Int, { description: 'Number of items per page' })
  limit: number;

  @Field(() => Int, { description: 'Current page number' })
  page: number;

  @Field(() => Int, { description: 'Total number of pages' })
  pages: number;

  @Field(() => Int, { description: 'Total number of items' })
  total: number;
}
