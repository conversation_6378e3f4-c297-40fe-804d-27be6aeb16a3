# Test GraphQL API locally
$headers = @{
    "Content-Type" = "application/json"
    "Cookie" = "access_token=VTgxeHgyUFhuUWtuOW56YVA3elVUTFV2Qkk3cDRxeXNxSXM4MWluMHVjZVROaWhHcXJCNVBGQzEvR1hIRzcyWDJpWFlYeEl1QkRjUUp2b3JsTWJvRE03NmEyRlllcDdETDhlVUVKUzZZMzMzVlM1VUlRR3NWSlQ1WnVZdDZRdzZUbWxiRlNXaitncHhoaXpmenV2Q1pURmR6VmMrZnRySDBBR0ZjcHdwWHFKNFIyT0tFUzM2czFDQ1htZlhBU3BYOVpvd2pkZW52bk4vTjhNczVQWi9CMExCQVRoZUNMVUlWWktKcHBiZVFTWm16WWRORHR5dmJsNGJ3WDFxdlV6QlJ4Mk9lQzcrYTV5Sk9LbGhsOUM2YWpaWFFid0EyOWxrdjBFMDlOQmt5M2dBYzVEa2hKSXNPMU11dEVvRysydG1DRnNKcGh1cnkwcndlZ054cmtxVTdyb1lpVmpZa3ZrdHFVYnZnMFA2RThvMDV2cTBqT3BOekNFRXJRMlZITFJWNjJ1Uk8vNjhCcW00ckZKdDdEa1B6dExCNERWVzlYS0JEQjJibXJZWnZxRHJiR0xRSjVqK2RMNVNPenVURFgvK2hhKytpNEtOSU4ybW02ZG1yTmdSR2VYOTU5ZW1JZy9qTDlHRGY0dkJ4YTQ1c1FERmdCMmYvb2sxTWEyS3R5WVFYVTJZSXJYWDlBQW1vbE5JNzhBb2pWY2JmMzU4a1BVVFJoZEFXTnhWU0h5a1YwTmM0ZmpSaVVSTnFDNWZsYnBCWkxieXdXTVp3MjR5b3JMb3MyNElKRzlpVXdzcFdrdXJ5eVN0L3Rwa0JGTUhpS2hpN0xjZXZEdVI5MGhyTE5XL3ROMS9rNXROSFBmdDNRMTk3ajkwOTBOQTdrMnBSYU1GZ0psbUp0OW9tR3hEV3RmektJYWNDVEcxMHkvSitiTVNER2VxNjl5cndIQURManUwQ3c5T294OHc2eC9WdkJ2T0YvUndpNnQrcDQ4aTlpay9JTFdLbTgrY3JTUGVEOXpIanVUU2tob0RtRW1JR3dWaXhwckFZM0tFaTJxMGw1U1doZm40STJNKzY5STZKV0R1OGEyeFY5WmdNUUhVSlQ5N0srSjg4VDR3Y3VxaDVFTFpFVHFwYk9ZZXRzNjBmc21KcnZaZ2NBd0hlRDJXTU1yc04zZnF6QVJ6aXRGT04xeTVHY015VXRBNnNCM1ZrWjE2NUlUeHRjWVBrOWIvdUhNdVJmcUd2Ui9tREMzZUtnTDFQY3VGWWxteDdDY2Rlbng0dnRiMFlLWHBpRUV0WFYxTlF2VjRHSEVVOGlYOG1lN3NnaUdtVG9EZkIyaFBjVVJ6d1dKNUxhUXR6N2plNHpNWitPcnlsZU1EYm81cXVCWEJtWU9WcXJaMUQrc1o2bk5nbUtpNUM2WVh1NjhiVDkvbGE3V2ZSTEtwOXBpMXhkcEhzZGN5bDJYY25kUm9DbEpqNmdyVlhVczd1Qk9yNDV5V0FGN0tmQT09LS1UU2FuMDlpd3Fwdld5OURaLS04R2M5MERFK2Z2RnZnODJUSEFzYWNRPT0%3D"
}

$body = @{
    query = "query GetCurrentUserJWT { authMe { iss sub aud exp iat jti sid azp ent_set perm_v amr auth_time email } }"
} | ConvertTo-Json

Write-Host "Testing localhost:3040..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3040/graphql" -Method Post -Headers $headers -Body $body
    Write-Host "Success! Response:"
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}

Write-Host "`nTesting health endpoint..."
try {
    $health = Invoke-RestMethod -Uri "http://localhost:3040/health" -Method Get
    Write-Host "Health check response:"
    $health | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Health check error: $($_.Exception.Message)"
}
