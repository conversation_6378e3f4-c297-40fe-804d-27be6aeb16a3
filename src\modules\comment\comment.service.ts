import { Injectable, NotFoundException, BadRequestException, Logger, Inject, forwardRef } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { PrismaService } from "../../prisma/prisma.service";
import { CommentPayload, commentSelect } from "./types/comment.types";
import { ScopeFilterService } from "../auth/src/scope-filter.service";
import { ScopeContext } from "../auth/src/types/scope.types";
import { TicketService } from "../ticket/ticket.service";

export interface CreateCommentWithScopeParams {
  message: string;
  ticketId: string;
  authorId: string;
  // User data fields from /auth/users/{id} endpoint (same as ticket system)
  firstName?: string;
  lastName?: string;
  email?: string;
  // Partner information fields (same as ticket system)
  partnerUserId?: string;
  partnerRole?: string;
  partnerOrgId?: string;
  entSet?: string;
  // User display fields (available in Comment model)
  authorName?: string;
  authorEmail?: string;
}

@Injectable()
export class CommentService {
  private readonly logger = new Logger(CommentService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly scopeFilterService: ScopeFilterService,
    @Inject(forwardRef(() => TicketService))
    private readonly ticketService: TicketService
  ) {}

  async create(data: Prisma.CommentCreateInput): Promise<CommentPayload> {
    return this.prisma.comment.create({
      data,
      select: commentSelect
    });
  }

  async findByTicketId(ticketId: string): Promise<CommentPayload[]> {
    return this.prisma.comment.findMany({
      where: {
        ticketId
      },
      select: commentSelect,
      orderBy: {
        createdAt: "desc"
      }
    });
  }

  async delete(where: Prisma.CommentWhereUniqueInput): Promise<CommentPayload> {
    const comment = await this.prisma.comment.findUnique({
      where,
      select: commentSelect
    });

    if (!comment) {
      throw new NotFoundException(`Comment with ID ${where.id} not found`);
    }

    return this.prisma.comment.delete({
      where,
      select: commentSelect
    });
  }

  // Scope-aware methods

  /**
   * Create comment with scope-based access control
   */
  async createWithScope(
    scopeContext: ScopeContext,
    data: CreateCommentWithScopeParams
  ): Promise<CommentPayload> {
    this.logger.log(`💬 [COMMENT SERVICE] Creating comment for ticket ${data.ticketId} by user ${scopeContext.userId}`);

    // First, verify that the user can access the ticket they're trying to comment on
    const ticket = await this.ticketService.findUniqueWithScope(scopeContext, { id: data.ticketId });
    if (!ticket) {
      throw new NotFoundException('Ticket not found or access denied');
    }

    this.logger.log(`✅ [COMMENT SERVICE] User ${scopeContext.userId} has access to ticket ${data.ticketId}`);

    // Create the comment with all user context fields (same as ticket system)
    const createInput: Prisma.CommentCreateInput = {
      message: data.message,
      authorId: data.authorId,
      ticket: {
        connect: { id: data.ticketId }
      },
      // Include user data fields fetched from auth API
      ...(data.firstName && { firstName: data.firstName }),
      ...(data.lastName && { lastName: data.lastName }),
      ...(data.email && { email: data.email }),
      // Include partner-related fields
      ...(data.partnerUserId && { partnerUserId: data.partnerUserId }),
      ...(data.partnerRole && { partnerRole: data.partnerRole }),
      ...(data.partnerOrgId && { partnerOrgId: data.partnerOrgId }),
      ...(data.entSet && { entSet: data.entSet }),
      // Include user display fields
      ...(data.authorName && { authorName: data.authorName }),
      ...(data.authorEmail && { authorEmail: data.authorEmail })
    };

    // Log all the user context data being stored
    this.logger.log(`📝 [COMMENT SERVICE] Creating comment with user context (all fields now stored):`, {
      authorId: data.authorId,
      authorName: data.authorName,
      authorEmail: data.authorEmail,
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      partnerUserId: data.partnerUserId,
      partnerRole: data.partnerRole,
      partnerOrgId: data.partnerOrgId,
      entSet: data.entSet
    });

    return this.prisma.comment.create({
      data: createInput,
      select: commentSelect
    });
  }

  /**
   * Find comments by ticket ID with scope-based access control
   */
  async findByTicketIdWithScope(
    scopeContext: ScopeContext,
    ticketId: string
  ): Promise<CommentPayload[]> {
    this.logger.log(`🔍 [COMMENT SERVICE] Finding comments for ticket ${ticketId} with scope filtering for user ${scopeContext.userId}`);

    // First, verify that the user can access the ticket
    const ticket = await this.ticketService.findUniqueWithScope(scopeContext, { id: ticketId });
    if (!ticket) {
      throw new NotFoundException('Ticket not found or access denied');
    }

    this.logger.log(`✅ [COMMENT SERVICE] User ${scopeContext.userId} has access to ticket ${ticketId}`);

    // Apply scope-based filtering to comments
    const scopedWhere = this.scopeFilterService.applyCommentFilters(scopeContext, { ticketId });

    return this.prisma.comment.findMany({
      where: scopedWhere,
      select: commentSelect,
      orderBy: {
        createdAt: "desc"
      }
    });
  }

  /**
   * Delete comment with scope-based access control
   */
  async deleteWithScope(
    scopeContext: ScopeContext,
    where: Prisma.CommentWhereUniqueInput
  ): Promise<CommentPayload> {
    this.logger.log(`🗑️ [COMMENT SERVICE] Deleting comment ${where.id} with scope validation for user ${scopeContext.userId}`);

    // First, find the comment and verify it exists
    const comment = await this.prisma.comment.findUnique({
      where,
      select: {
        ...commentSelect,
        ticketId: true // We need the ticket ID to check access
      }
    });

    if (!comment) {
      throw new NotFoundException(`Comment with ID ${where.id} not found`);
    }

    // Verify that the user can access the ticket this comment belongs to
    const ticket = await this.ticketService.findUniqueWithScope(scopeContext, { id: comment.ticketId });
    if (!ticket) {
      throw new NotFoundException('Comment not found or access denied');
    }

    // Check if user can delete this comment based on scope permissions
    if (!this.scopeFilterService.canDeleteComment(scopeContext, comment)) {
      this.scopeFilterService.logAccessDecision(scopeContext, 'comment', 'delete', false);
      throw new BadRequestException('Insufficient permissions to delete this comment');
    }

    this.scopeFilterService.logAccessDecision(scopeContext, 'comment', 'delete', true);

    return this.prisma.comment.delete({
      where,
      select: commentSelect
    });
  }
}
