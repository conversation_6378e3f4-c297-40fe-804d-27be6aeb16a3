import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { Logger, UseGuards } from '@nestjs/common';
import { AuthSharedService } from './auth.service';
import { GraphQLAuthGuard } from './guards/graphql-auth.guard';
import { JWTPayloadGraphQL, UserGraphQL, CurrentUserGraphQL, UserListResponseGraphQL } from './types/graphql.types';
import { GetUsersDto } from './dto/get-users.dto';
import { ScopeGuard, SupportAccess } from './guards/scope.guard';
import { UserDataService } from './user-data.service';

@Resolver()
export class AuthResolver {
  private readonly logger = new Logger(AuthResolver.name);

  constructor(
    private readonly authService: AuthSharedService,
    private readonly userDataService: UserDataService
  ) {}

  @Query(() => JWTPayloadGraphQL, { 
    name: 'authMe', 
    description: 'Get current user JWT payload from authentication service - returns only actual JWT fields' 
  })
  async getCurrentUser(@Context() context: any): Promise<any> {
    this.logger.log('🔍 [AUTH RESOLVER] authMe query called');

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Authenticating user and extracting JWT payload...');

      // Use the corrected method name from support-backend
      const jwtPayload = await this.authService.authenticateUserFromCookies(cookies);

      this.logger.log(`✅ [AUTH RESOLVER] JWT payload retrieved successfully for user: ${jwtPayload.sub}`);
      this.logger.log(`📋 [AUTH RESOLVER] JWT payload fields: ${Object.keys(jwtPayload).join(', ')}`);
      
      return jwtPayload;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting current user: ${error.message}`);
      throw error;
    }
  }

  @Query(() => [UserGraphQL], { 
    name: 'authUsers', 
    description: 'Get all users from external auth service' 
  })
  @UseGuards(GraphQLAuthGuard)
  async getAllUsers(@Context() context: any): Promise<any[]> {
    this.logger.log('🔍 [AUTH RESOLVER] authUsers query called');

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log('🔐 [AUTH RESOLVER] Fetching users from external auth service...');

      const users = await this.authService.getAllUsers(cookies);

      this.logger.log(`✅ [AUTH RESOLVER] Users retrieved successfully: ${Array.isArray(users) ? users.length : 'unknown'} users`);

      return users;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting all users: ${error.message}`);
      throw error;
    }
  }

  @Query(() => UserGraphQL, { 
    name: 'authUser', 
    description: 'Get specific user by ID from external auth service' 
  })
  @UseGuards(GraphQLAuthGuard)
  async getUserById(
    @Args('id', { type: () => String }) id: string,
    @Context() context: any
  ): Promise<any> {
    this.logger.log(`🔍 [AUTH RESOLVER] authUser query called for ID: ${id}`);

    const request = context.req;

    if (!request) {
      this.logger.error('❌ [AUTH RESOLVER] Request context not available');
      throw new Error('Request context not available');
    }

    // Extract cookies from request
    const cookies = request.cookies || {};
    this.logger.log(`🍪 [AUTH RESOLVER] Cookies available: ${Object.keys(cookies).join(', ') || 'NONE'}`);

    if (!cookies.access_token) {
      this.logger.warn('❌ [AUTH RESOLVER] No access token found in cookies');
      throw new Error('Authentication required - no access token');
    }

    try {
      this.logger.log(`🔐 [AUTH RESOLVER] Fetching user ${id} from external auth service...`);

      const user = await this.authService.getUserById(id, cookies);

      this.logger.log(`✅ [AUTH RESOLVER] User ${id} retrieved successfully`);

      return user;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Error getting user ${id}: ${error.message}`);
      throw error;
    }
  }

  @Query(() => String, {
    name: 'authPing',
    description: 'Simple ping endpoint to test authentication GraphQL setup'
  })
  async authPing(): Promise<string> {
    this.logger.log('🔍 [AUTH RESOLVER] authPing query called');
    return 'Auth GraphQL resolver is working!';
  }

  // Additional user management queries that match the schema
  @Query(() => JWTPayloadGraphQL, {
    name: 'me',
    description: 'Get current user information (alias for authMe)'
  })
  async me(@Context() context: any): Promise<any> {
    // This is an alias for authMe to match the schema
    return this.getCurrentUser(context);
  }

  @Query(() => UserGraphQL, {
    name: 'user',
    description: 'Get user by ID (alias for authUser)'
  })
  @UseGuards(GraphQLAuthGuard)
  async user(
    @Args('id', { type: () => String }) id: string,
    @Context() context: any
  ): Promise<any> {
    // This is an alias for authUser to match the schema
    return this.getUserById(id, context);
  }

  @Query(() => UserListResponseGraphQL, {
    name: 'users',
    description: 'Get all users with optional filtering'
  })
  @UseGuards(GraphQLAuthGuard)
  @SupportAccess()
  async users(
    @Args('params', { nullable: true }) params?: GetUsersDto,
    @Context() context?: any
  ): Promise<UserListResponseGraphQL> {
    this.logger.log('🔍 [AUTH RESOLVER] users query called');

    // Get all users
    const allUsers = await this.getAllUsers(context);

    // Apply filtering if params provided
    let filteredUsers = allUsers;
    if (params) {
      if (params.active !== undefined) {
        filteredUsers = filteredUsers.filter(user => user.active === params.active);
      }
      if (params.role) {
        filteredUsers = filteredUsers.filter(user => user.role === params.role);
      }
      if (params.search) {
        const searchTerm = params.search.toLowerCase();
        filteredUsers = filteredUsers.filter(user =>
          user.email?.toLowerCase().includes(searchTerm) ||
          user.firstName?.toLowerCase().includes(searchTerm) ||
          user.lastName?.toLowerCase().includes(searchTerm)
        );
      }
    }

    // Apply pagination
    const limit = params?.limit || 10;
    const page = params?.page || 1;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    return {
      items: paginatedUsers,
      limit,
      page,
      pages: Math.ceil(filteredUsers.length / limit),
      total: filteredUsers.length
    };
  }

  @Query(() => [UserGraphQL], {
    name: 'activeUsers',
    description: 'Get all active users'
  })
  @UseGuards(GraphQLAuthGuard)
  @SupportAccess()
  async activeUsers(@Context() context: any): Promise<any[]> {
    this.logger.log('🔍 [AUTH RESOLVER] activeUsers query called');

    // Get all users and filter for active ones
    const allUsers = await this.getAllUsers(context);
    return allUsers.filter(user => user.active !== false);
  }

  @Query(() => [UserGraphQL], {
    name: 'usersByRole',
    description: 'Get users by role'
  })
  @UseGuards(GraphQLAuthGuard)
  @SupportAccess()
  async usersByRole(
    @Args('role', { type: () => String }) role: string,
    @Context() context: any
  ): Promise<any[]> {
    this.logger.log(`🔍 [AUTH RESOLVER] usersByRole query called for role: ${role}`);

    // Get all users and filter by role
    const allUsers = await this.getAllUsers(context);
    return allUsers.filter(user => user.role === role);
  }

  @Query(() => String, {
    name: 'validateToken',
    description: 'Validate access token'
  })
  async validateToken(
    @Args('token', { type: () => String }) token: string
  ): Promise<string> {
    this.logger.log('🔍 [AUTH RESOLVER] validateToken query called');

    try {
      // Verify the token using the auth service
      const payload = await this.authService.verifyToken(token);
      return `Token is valid for user: ${payload.sub}`;
    } catch (error) {
      this.logger.error(`❌ [AUTH RESOLVER] Token validation failed: ${error.message}`);
      throw new Error('Invalid token');
    }
  }
}
